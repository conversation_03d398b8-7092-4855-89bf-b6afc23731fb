// Renderers / Shaders /////////////////////////////////////////////////////////////////////
export const ShaderChunk: {
    alphahash_fragment: string;
    alphahash_pars_fragment: string;
    alphamap_fragment: string;
    alphamap_pars_fragment: string;
    alphatest_fragment: string;
    alphatest_pars_fragment: string;
    aomap_fragment: string;
    aomap_pars_fragment: string;
    batching_pars_vertex: string;
    begin_vertex: string;
    beginnormal_vertex: string;
    bsdfs: string;
    iridescence_fragment: string;
    bumpmap_pars_fragment: string;
    clipping_planes_fragment: string;
    clipping_planes_pars_fragment: string;
    clipping_planes_pars_vertex: string;
    clipping_planes_vertex: string;
    color_fragment: string;
    color_pars_fragment: string;
    color_pars_vertex: string;
    color_vertex: string;
    common: string;
    cube_uv_reflection_fragment: string;
    defaultnormal_vertex: string;
    displacementmap_pars_vertex: string;
    displacementmap_vertex: string;
    emissivemap_fragment: string;
    emissivemap_pars_fragment: string;
    colorspace_fragment: string;
    colorspace_pars_fragment: string;
    envmap_fragment: string;
    envmap_common_pars_fragment: string;
    envmap_pars_fragment: string;
    envmap_pars_vertex: string;
    envmap_physical_pars_fragment: string;
    envmap_vertex: string;
    fog_vertex: string;
    fog_pars_vertex: string;
    fog_fragment: string;
    fog_pars_fragment: string;
    gradientmap_pars_fragment: string;
    lightmap_pars_fragment: string;
    lights_lambert_fragment: string;
    lights_lambert_pars_fragment: string;
    lights_pars_begin: string;
    lights_toon_fragment: string;
    lights_toon_pars_fragment: string;
    lights_phong_fragment: string;
    lights_phong_pars_fragment: string;
    lights_physical_fragment: string;
    lights_physical_pars_fragment: string;
    lights_fragment_begin: string;
    lights_fragment_maps: string;
    lights_fragment_end: string;
    logdepthbuf_fragment: string;
    logdepthbuf_pars_fragment: string;
    logdepthbuf_pars_vertex: string;
    logdepthbuf_vertex: string;
    map_fragment: string;
    map_pars_fragment: string;
    map_particle_fragment: string;
    map_particle_pars_fragment: string;
    metalnessmap_fragment: string;
    metalnessmap_pars_fragment: string;
    morphcolor_vertex: string;
    morphnormal_vertex: string;
    morphtarget_pars_vertex: string;
    morphtarget_vertex: string;
    normal_fragment_begin: string;
    normal_fragment_maps: string;
    normal_pars_fragment: string;
    normal_pars_vertex: string;
    normal_vertex: string;
    normalmap_pars_fragment: string;
    clearcoat_normal_fragment_begin: string;
    clearcoat_normal_fragment_maps: string;
    clearcoat_pars_fragment: string;
    iridescence_pars_fragment: string;
    opaque_fragment: string;
    packing: string;
    premultiplied_alpha_fragment: string;
    project_vertex: string;
    dithering_fragment: string;
    dithering_pars_fragment: string;
    roughnessmap_fragment: string;
    roughnessmap_pars_fragment: string;
    shadowmap_pars_fragment: string;
    shadowmap_pars_vertex: string;
    shadowmap_vertex: string;
    shadowmask_pars_fragment: string;
    skinbase_vertex: string;
    skinning_pars_vertex: string;
    skinning_vertex: string;
    skinnormal_vertex: string;
    specularmap_fragment: string;
    specularmap_pars_fragment: string;
    tonemapping_fragment: string;
    tonemapping_pars_fragment: string;
    transmission_fragment: string;
    transmission_pars_fragment: string;
    uv_pars_fragment: string;
    uv_pars_vertex: string;
    uv_vertex: string;
    worldpos_vertex: string;

    background_vert: string;
    background_frag: string;
    backgroundCube_vert: string;
    backgroundCube_frag: string;
    cube_vert: string;
    cube_frag: string;
    depth_vert: string;
    depth_frag: string;
    distanceRGBA_vert: string;
    distanceRGBA_frag: string;
    equirect_vert: string;
    equirect_frag: string;
    linedashed_vert: string;
    linedashed_frag: string;
    meshbasic_vert: string;
    meshbasic_frag: string;
    meshlambert_vert: string;
    meshlambert_frag: string;
    meshmatcap_vert: string;
    meshmatcap_frag: string;
    meshnormal_vert: string;
    meshnormal_frag: string;
    meshphong_vert: string;
    meshphong_frag: string;
    meshphysical_vert: string;
    meshphysical_frag: string;
    meshtoon_vert: string;
    meshtoon_frag: string;
    points_vert: string;
    points_frag: string;
    shadow_vert: string;
    shadow_frag: string;
    sprite_vert: string;
    sprite_frag: string;
};
