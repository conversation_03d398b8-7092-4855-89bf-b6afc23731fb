{"name": "evacuation-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:ip": "node start-with-ip.js", "dev:host": "vite --host 0.0.0.0", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.10.0", "echarts": "^5.6.0", "three": "^0.178.0", "vue": "^3.5.13"}, "devDependencies": {"@types/node": "^24.2.0", "@types/three": "^0.178.1", "@vitejs/plugin-vue": "^5.2.3", "typescript": "^5.9.2", "vite": "^6.3.5"}}